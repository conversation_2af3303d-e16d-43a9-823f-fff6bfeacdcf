from typing import Any

from config import settings

from .base import ApplicationError


__all__ = ['DocumentValidationError', 'MaximumDocumentsNumberExceeded', 'MaximumDocumentsSizeExceeded']


class DocumentValidationError(ApplicationError):
    """Raised when the document validation failed."""

    pass


class MaximumDocumentsNumberExceeded(ApplicationError):
    """Raised when docs count exceeds a number."""

    def __init__(self, filename: str):
        self.max_doc_count = settings.document_storage.max_docs_per_conversation
        self.filename = filename
        message = (
            f'File upload error [{filename}]. Your file could not be added because it exceeds the maximum file limit '
            f'({self.max_doc_count} attached files).'
        )
        super().__init__(message)


class MaximumDocumentsSizeExceeded(ApplicationError):
    """Raised when docs size exceeds a max total size."""

    def __init__(self, projected_size: Any):
        self.projected_size = projected_size
        self.max_docs_size = settings.document_storage.max_conversation_size
        super().__init__(
            f'Total document size ({projected_size} bytes) would exceed the maximum allowed '
            f'({self.max_docs_size} bytes) per conversation'
        )
