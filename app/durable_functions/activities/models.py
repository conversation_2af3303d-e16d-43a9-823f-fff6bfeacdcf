from typing import Any

from pydantic import HttpUrl

from constants.durable_functions import EventType, ExctractStatus, OrchestratorInputType
from constants.extracted_data import DataSourceType
from durable_functions.utils import DFBaseModel
from durable_functions.utils.models import FinalExtractionDataResults


__all__ = [
    'SendNotificationActivityInput',
    'UpdateProcessingStatusActivityInput',
    'ExtractDocumentTextActivityInput',
    'ChunkDocumentActivityInput',
    'ChunkDocumentActivityOutput',
    'SendQueueMessageActivityInput',
    'ReadPromptActivityInput',
    'ExtractDataActivityInput',
    'SaveExtractionDataActivityInput',
    'AggregateMultiSourceDataActivityInput',
    'SaveAggregatedResultsToBlobActivityInput',
    'SendFinalQueueMessageActivityInput',
]


class SendNotificationActivityInput(DFBaseModel):
    event_type: EventType
    data: dict[str, Any]
    signalr_user_id: str | None = None


class UpdateProcessingStatusActivityInput(DFBaseModel):
    message_id: str
    status: str
    message: str
    metadata: dict[str, Any] = {}


class ExtractDocumentTextActivityInput(DFBaseModel):
    blob_url: str
    message_id: str
    file_name: str


class BaseExtractDocumentTextActivityOutput(DFBaseModel):
    message_id: str
    file_name: str
    status: ExctractStatus


class ExtractDocumentTextActivityOutput(BaseExtractDocumentTextActivityOutput):
    extraction_url: str
    text_content: str
    metadata: dict[str, Any]


class ExtractDocumentTextActivityOutputFailed(BaseExtractDocumentTextActivityOutput):
    error: str
    file_is_corrupted: bool | None = None


class ChunkDocumentActivityInput(DFBaseModel):
    message_id: str
    file_name: str
    extraction_url: str
    text_content: str
    metadata: dict[str, Any]


class ChunkDocumentActivityOutput(DFBaseModel):
    message_id: str
    file_name: str
    chunk_count: int
    chunk_urls: list[dict[str, Any]]


class SendQueueMessageActivityInput(DFBaseModel):
    message_id: str
    file_name: str
    chunk_count: int
    chunk_urls: list[dict[str, Any]]
    signalr_user_id: str
    input_type: OrchestratorInputType = OrchestratorInputType.Document


class ReadPromptActivityInput(DFBaseModel):
    prompt_url: str


class ExtractDataActivityInput(DFBaseModel):
    chunk_url: HttpUrl | None = None
    text_content: str | None = None


class SaveExtractionDataActivityInput(DFBaseModel):
    message_id: str
    extraction_data: FinalExtractionDataResults
    data_source_type: DataSourceType


class AggregateMultiSourceDataActivityInput(DFBaseModel):
    message_id: str
    source_results: list[tuple[DataSourceType, FinalExtractionDataResults]]


class SaveAggregatedResultsToBlobActivityInput(DFBaseModel):
    message_id: str
    aggregated_data: FinalExtractionDataResults


class SendFinalQueueMessageActivityInput(DFBaseModel):
    message_id: str
    blob_url: str
    signalr_user_id: str
