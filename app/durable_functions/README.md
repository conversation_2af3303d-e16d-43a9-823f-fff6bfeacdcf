# Document Processing Azure Durable Functions

This module contains Azure Durable Functions for document processing in the KX GenAI Qual application. The functions are integrated with the existing FastAPI application and share the same repository/service layer to avoid code duplication.

## Prerequisites

- ODBC Driver 17 for SQL Server must be installed on your system

### Installing ODBC Driver on macOS

```bash
# Install Homebrew if not already installed
% /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install.sh)"

# Add Microsoft's tap and update Homebrew
% brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
% brew update

# Install the driver (accepts EULA automatically)
% HOMEBREW_ACCEPT_EULA=Y brew install msodbcsql17 mssql-tools unixodbc
```

### Installing ODBC Driver on Ubuntu server

```bash
curl https://packages.microsoft.com/keys/microsoft.asc | sudo tee /etc/apt/trusted.gpg.d/microsoft.asc
curl https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/prod.list | sudo tee /etc/apt/sources.list.d/mssql-release.list

sudo apt-get update

sudo ACCEPT_EULA=Y apt-get install -y msodbcsql17
sudo ACCEPT_EULA=Y apt-get install -y mssql-tools

echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc
source ~/.bashrc

```

## Overview

The document processing workflow consists of the following steps:

1.  **File Upload**: The API uploads files to Azure Blob Storage at `/uploads/{message_id}/{filename}`
2.  **Queue Trigger**: A message is added to a unified queue to trigger content processing.
3.  **Text Extraction**: Document Intelligence extracts text from the document.
4.  **Extraction Storage**: The extraction result is saved to `/extracted/{message_id}/{filename}`
5.  **Chunking**: The extracted text is chunked using LangChain's RecursiveCharacterTextSplitter.
6.  **Chunk Storage**: Chunks are saved to `/chunks/{original_filename_or_uuid}_chunk_{chunk_index}.json`
7.  **Unified Processing**: A new orchestrator handles parallel processing of documents and text prompts, aggregates the results, and saves them.
8.  **Status Updates**: The API is notified of processing status changes.
9.  **SignalR Notifications**: The frontend is notified of status changes via SignalR.

## Architecture Integration

The durable functions are integrated with the main FastAPI application by:
- Sharing the same Python path through `PYTHONPATH` configuration
- Using existing repository and service classes through dependency injection
- Leveraging established database connections and configurations
- Maintaining consistency with the main application's error handling and logging

## Folder Structure

- `activities/`: Activity functions for each step of the workflow
- `orchestrators/`: Orchestrator functions to coordinate the workflow, including the new `unified_processing_orchestrator`.
- `triggers/`: HTTP and queue triggers to start the workflow
- `utils/`: Utility modules for blob storage, document intelligence, chunking, and API integration
- `application/`: Configuration and shared application code
- `repositories/`: Repository classes for data access (shared with main app)

## Activities

The following activity functions are available:

- `extract_document_text`: Extracts text from a document using Document Intelligence
- `chunk_document`: Chunks the extracted text content of a document
- `update_processing_status`: Updates the processing status of a document via the API
- `send_notification`: Sends a notification via SignalR
- `aggregate_multi_source_data`: Aggregates extraction data from multiple sources.
- `save_aggregated_results_to_blob`: Saves aggregated results to blob storage.
- `send_final_queue_message`: Sends a final queue message for further processing.


## Orchestrators

The following orchestrators are available:

- `document_processing_orchestrator`: Coordinates the document processing workflow, including text extraction, chunking, and status updates
- `unified_processing_orchestrator`: Orchestrates the parallel processing of documents and text prompts, aggregates the results, and saves them.

## HTTP Triggers

The following HTTP triggers are available:

- `check-status/{instance_id}`: Checks the status of a Durable Functions orchestration instance
- `process-document`: HTTP endpoint to start document processing

## Queue Triggers

The following queue triggers are available:

- `process_unified_queue`: Triggered by messages in the `content-analysis-queue` queue to start the appropriate processing orchestration.

## Environment Variables Configuration

The application uses a dual configuration approach:

1.  **`local.env`**: Contains durable functions-specific settings loaded by Pydantic with nested structure (using `__` delimiter)
2.  **`local.settings.json`**: Contains Azure Functions runtime and core application settings with flat structure

Configuration is loaded from environment-specific `.env` files and `local.settings.json` for Azure Functions runtime.

### Configuration File Organization

#### Variables in local.settings.json (Azure Functions Runtime)
- Azure Functions runtime settings (`AzureWebJobsStorage`, `FUNCTIONS_WORKER_RUNTIME`, etc.)
- Core application settings (Database, Azure OpenAI)
- Flat structure environment variables

#### Variables in local.env (Durable Functions Specific)
- Durable functions-specific settings with nested structure
- Azure Storage and Queue settings for document processing
- Document Intelligence settings
- SignalR settings
- Chunking configuration

### Core Environment Variables

#### Environment Configuration
- `ENVIRONMENT`: Application environment
  - **Required**: Yes
  - **Possible Values**: `local`, `dev`, `qa`, `stage`, `prod`, `test`
  - **Example**: `local`
  - **Description**: Determines which environment configuration to load

#### Azure Functions Runtime
- `AzureWebJobsStorage`: Azure Storage connection string for Functions runtime
  - **Required**: Yes
  - **Example**: `UseDevelopmentStorage=true` (for local development)
  - **Description**: Storage account used by Azure Functions runtime for internal operations

- `FUNCTIONS_WORKER_RUNTIME`: The runtime for the Azure Functions worker
  - **Required**: Yes
  - **Example**: `python`
  - **Description**: Specifies the language runtime for Azure Functions

- `AzureWebJobsFeatureFlags`: Feature flags for Azure Functions
  - **Required**: No
  - **Example**: `EnableWorkerIndexing`
  - **Description**: Enables specific Azure Functions features

- `PYTHONPATH`: Python module search path
  - **Required**: Yes
  - **Example**: `.:app`
  - **Description**: Allows durable functions to import from the main application

### Database Configuration
- `DB_HOST`: Database server hostname
  - **Required**: Yes
  - **Example**: `127.0.0.1`
  - **Description**: MS SQL Server hostname or IP address

- `DB_PORT`: Database server port
  - **Required**: Yes
  - **Example**: `1433`
  - **Description**: MS SQL Server port number

- `DB_USER`: Database username
  - **Required**: Yes
  - **Example**: `sa`
  - **Description**: Database authentication username

- `DB_PASSWORD`: Database password
  - **Required**: Yes
  - **Example**: `P@ssw0rd_2024_SQL_Secure!`
  - **Description**: Database authentication password

- `DB_NAME`: Database name
  - **Required**: Yes
  - **Example**: `genai_quals`
  - **Description**: Target database name (test_ prefix added automatically for test environment)

- `DB_DRIVER`: ODBC driver name
  - **Required**: No
  - **Default**: `ODBC+Driver+17+for+SQL+Server`
  - **Example**: `ODBC+Driver+17+for+SQL+Server`
  - **Description**: ODBC driver for SQL Server connection

### Azure Storage Configuration
- `BLOB_STORAGE_SETTINGS__CONNECTION_STRING`: Azure Blob Storage connection string
  - **Required**: Yes
  - **Description**: Connection string for Azure Blob Storage (use Azurite for local development). Use the "Blob Storage" example from https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=visual-studio%2Cblob-storage#http-connection-strings

- `BLOB_STORAGE_SETTINGS__DOCUMENT_CONTAINER_NAME`: Document blob container name
  - **Required**: No
  - **Default**: `documents`
  - **Example**: `documents`
  - **Description**: Container name for storing documents and processing results

- `BLOB_STORAGE_SETTINGS__COUNTRIES_CONTAINER_NAME`: Countries blob container name
  - **Required**: No
  - **Default**: `countries`
  - **Example**: `countries`
  - **Description**: Container name for storing coutnries and processing results

- `QUEUE_SETTINGS__CONNECTION_STRING`: Azure Queue Storage connection string
  - **Required**: Yes
  - **Description**: Connection string for Azure Queue Storage. Use the "Queue Storage" example from https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=visual-studio%2Cblob-storage#http-connection-strings

- `QUEUE_SETTINGS__CONTENT_PROCESSING_QUEUE_NAME`: Content processing queue name
  - **Required**: No
  - **Default**: `content-analysis-queue`
  - **Example**: `content-analysis-queue`
  - **Description**: Queue name for unified content processing messages

- `QUEUE_SETTINGS__CONTENT_PROCESSING_QUEUE_CHUNKED`: Chunked content processing queue name
  - **Required**: No
  - **Default**: `content-analysis-chunked`
  - **Example**: `content-analysis-chunked`
  - **Description**: Queue name for chunked content processing messages

- `QUEUE_SETTINGS__CONTENT_PROCESSING_QUEUE_EXTRACTED`: Extracted content processing queue name
  - **Required**: No
  - **Default**: `content-analysis-extracted`
  - **Example**: `content-analysis-extracted`
  - **Description**: Queue name for extracted content processing messages

### Document Intelligence Configuration
- `DOCUMENT_INTELLIGENCE_SETTINGS__ENDPOINT`: Document Intelligence service endpoint
  - **Required**: Yes
  - **Example**: `https://your-doc-intelligence.cognitiveservices.azure.com/`
  - **Description**: Azure Document Intelligence service endpoint URL

- `DOCUMENT_INTELLIGENCE_SETTINGS__KEY`: Document Intelligence API key
  - **Required**: Yes
  - **Example**: `your-32-character-api-key-here`
  - **Description**: API key for Azure Document Intelligence service

- `DOCUMENT_INTELLIGENCE_SETTINGS__MODEL_NAME`: Document Intelligence model
  - **Required**: No
  - **Default**: `prebuilt-layout`
  - **Example**: `prebuilt-layout`
  - **Description**: Model name for document analysis

### Azure OpenAI Configuration
- `AZURE_OPENAI_ENDPOINT`: Azure OpenAI service endpoint
  - **Required**: Yes
  - **Example**: `https://your-openai.openai.azure.com/`
  - **Description**: Azure OpenAI service endpoint URL

- `AZURE_OPENAI_KEY`: Azure OpenAI API key
  - **Required**: Yes
  - **Example**: `your-32-character-openai-api-key`
  - **Description**: API key for Azure OpenAI service

- `AZURE_OPENAI_MODEL`: OpenAI model name
  - **Required**: Yes
  - **Example**: `gpt-35-turbo`
  - **Description**: OpenAI model to use for processing

- `AZURE_OPENAI_API_VERSION`: OpenAI API version
  - **Required**: Yes
  - **Example**: `2024-08-01-preview`
  - **Description**: API version for Azure OpenAI service

- `AZURE_OPENAI_DEFAULT_TEMPERATURE`: Default temperature for OpenAI requests
  - **Required**: No
  - **Default**: `0.0`
  - **Example**: `0.0`
  - **Description**: Default temperature setting for AI responses

### SignalR Configuration
- `SIGNALR_SETTINGS__CONNECTION_STRING`: Azure SignalR connection string
  - **Required**: Yes
  - **Example**: `Endpoint=https://your-signalr.service.signalr.net;AccessKey=your-access-key;Version=1.0;`
  - **Description**: Connection string for Azure SignalR service

- `SIGNALR_SETTINGS__HUB_NAME`: SignalR hub name
  - **Required**: No
  - **Default**: `qualMessageHub`
  - **Example**: `localhub`
  - **Description**: Name of the SignalR hub for real-time notifications

### Chunking Configuration
- `CHUNKING_SETTINGS__CHUNK_SIZE`: Chunk size in tokens
  - **Required**: No
  - **Default**: `8096`
  - **Example**: `8096`
  - **Description**: Maximum size of each text chunk in tokens

- `CHUNKING_SETTINGS__CHUNK_OVERLAP`: Chunk overlap in tokens
  - **Required**: No
  - **Default**: `512`
  - **Example**: `512`
  - **Description**: Number of overlapping tokens between chunks

- `CHUNKING_SETTINGS__ENCODING_NAME`: Tokenization encoding
  - **Required**: No
  - **Default**: `cl100k_base`
  - **Example**: `cl100k_base`
  - **Description**: Encoding method for tokenization

- `CHUNKING_SETTINGS__SEPARATORS`: Text separators for chunking
  - **Required**: No
  - **Default**: `['\n\n', '\n', '. ', ' ', '']`
  - **Example**: `['\n\n', '\n', '. ', ' ', '']`
  - **Description**: List of separators used for text splitting

## Chunking Strategy

The module uses LangChain's `RecursiveCharacterTextSplitter` with tiktoken for document chunking:

- **Strategy**: LangChain's `RecursiveCharacterTextSplitter` with tiktoken encoding
- **Chunk Size**: 8096 tokens (configurable via `CHUNKING_SETTINGS__CHUNK_SIZE`)
- **Chunk Overlap**: 512 tokens (configurable via `CHUNKING_SETTINGS__CHUNK_OVERLAP`)
- **Encoding**: cl100k_base (configurable via `CHUNKING_SETTINGS__ENCODING_NAME`)
- **Separators**: ['\n\n', '\n', '. ', ' ', ''] (configurable via `CHUNKING_SETTINGS__SEPARATORS`)

The chunking process:
1. Splits the document text into chunks using the configured strategy
2. Assigns a unique ID to each chunk
3. Adds metadata including the original document information
4. Calculates token count for each chunk
5. Saves chunks to blob storage with path `chat-attachments/chunks/{original_filename_or_uuid}_chunk_{chunk_index}.json`

## API Integration

The functions integrate with the main FastAPI application in multiple ways:

1. **Status Updates**: The API is notified of processing status changes via PUT requests to `/api/messages/{id}/data_processing`
2. **SignalR Notifications**: The frontend is notified of status changes via SignalR
3. **Shared Services**: Durable functions use the same repository and service classes as the main application
4. **Database Integration**: Direct database access through shared SQLAlchemy models and connections

## Function Execution Instructions

### Prerequisites

1. **Install Azure Functions Core Tools**:
   ```bash
   # macOS
   brew tap azure/functions
   brew install azure-functions-core-tools@4

   # Windows
   npm install -g azure-functions-core-tools@4

   # Linux
   wget -q https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb
   sudo dpkg -i packages-microsoft-prod.deb
   sudo apt-get update
   sudo apt-get install azure-functions-core-tools-4
   ```

2. **Install Python Dependencies**:
   ```bash
   cd app
   pip install -r requirements.txt
   ```

3. **Set up Local Storage Emulation** (for local development):
   ```bash
   # Start Azurite using Docker
   docker run -p 10000:10000 -p 10001:10001 -p 10002:10002 \
     mcr.microsoft.com/azure-storage/azurite
   ```

### Running the Functions

1. **Navigate to the app directory** (where `function_app.py` is located):
   ```bash
   cd app
   ```

2. **Start the Azure Functions runtime**:
   ```bash
   func start
   ```

   The functions will start on `http://localhost:7071` by default.

### Configuration Files

The Azure Functions runtime uses two main configuration files:

- **`function_app.py`**: Main initialization file that registers all blueprints (activities, orchestrators, triggers)
- **`local.settings.json`**: Local development configuration with environment variables
- **`host.json`**: Azure Functions host configuration

### Configuration File Structure

The Azure Durable Functions use a dual configuration approach:

1. **`local.env`**: Contains durable functions-specific settings (loaded by Pydantic settings)
2. **`local.settings.json`**: Contains Azure Functions runtime and core application settings

### Sample local.env Configuration

Create a `local.env` file in the app directory with the following durable functions-specific configuration:

```bash
# SignalR Configuration
SIGNALR_SETTINGS__HUB_NAME=localhub
SIGNALR_SETTINGS__CONNECTION_STRING='Endpoint=https://your-signalr.service.signalr.net;AccessKey=your-access-key;Version=1.0;'

# Azure Storage (using Azurite for local development)
BLOB_STORAGE_SETTINGS__CONNECTION_STRING='<PLACEHOLDER>'  # Use the "Blob Storage" example from https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=visual-studio%2Cblob-storage#http-connection-strings
BLOB_STORAGE_SETTINGS__CONTAINER_NAME=documents

# Azure Queue Storage
QUEUE_SETTINGS__CONNECTION_STRING='<PLACEHOLDER>'  # Use the "Queue Storage" example from https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=visual-studio%2Cblob-storage#http-connection-strings
QUEUE_SETTINGS__CONTENT_PROCESSING_QUEUE_NAME=content-analysis-queue
QUEUE_SETTINGS__CONTENT_PROCESSING_QUEUE_CHUNKED=content-analysis-chunked
QUEUE_SETTINGS__CONTENT_PROCESSING_QUEUE_EXTRACTED=content-analysis-extracted

# Document Intelligence
DOCUMENT_INTELLIGENCE_SETTINGS__ENDPOINT='https://your-doc-intelligence.cognitiveservices.azure.com/'
DOCUMENT_INTELLIGENCE_SETTINGS__KEY='your-32-character-api-key-here'
```

### Sample local.settings.json Configuration

The `local.settings.json` file contains Azure Functions runtime settings and core application configuration:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "AzureWebJobsFeatureFlags": "EnableWorkerIndexing",
    "ENVIRONMENT": "local",
    "FUNCTIONS_WORKER_RUNTIME": "python",
    "PYTHONPATH": ".:app",

    "AZURE_QUEUE_CONNECTION_STRING": "<PLACEHOLDER>",  # Use the "Queue Storage" example from https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=visual-studio%2Cblob-storage#http-connection-strings

    "DB_HOST": "127.0.0.1",
    "DB_PORT": "1433",
    "DB_USER": "sa",
    "DB_PASSWORD": "P@ssw0rd_2024_SQL_Secure!",
    "DB_NAME": "genai_quals",
    "DB_DRIVER": "ODBC+Driver+17+for+SQL+Server",

    "AZURE_OPENAI_ENDPOINT": "https://your-openai.openai.azure.com/",
    "AZURE_OPENAI_KEY": "your-32-character-openai-api-key",
    "AZURE_OPENAI_MODEL": "gpt-35-turbo",
    "AZURE_OPENAI_API_VERSION": "2024-08-01-preview"
  },
  "ConnectionStrings": {}
}
```

**Note**: The configuration is split between two files:
- **`local.env`**: Durable functions-specific settings with nested structure (using `__` delimiter)
- **`local.settings.json`**: Azure Functions runtime and core application settings with flat structure

## Using Azurite for Local Development

For local development, use Azurite to emulate Azure Storage services:

1. **Start Azurite using Docker**:
   ```bash
   docker run -p 10000:10000 -p 10001:10001 -p 10002:10002 \
     mcr.microsoft.com/azure-storage/azurite
   ```

2. **Azurite provides the following services**:
   - **Blob service**: `http://127.0.0.1:10000`
   - **Queue service**: `http://127.0.0.1:10001`
   - **Table service**: `http://127.0.0.1:10002`

3. **Use the development connection strings** shown in the sample configuration above

## Deployment

### Using Azure Functions Core Tools

Deploy to Azure Functions using the Azure Functions Core Tools:

```bash
# Login to Azure
az login

# Deploy the function app
func azure functionapp publish <your-function-app-name>
```

### Using Azure DevOps

Configure a CI/CD pipeline that:
1. Installs Python dependencies
2. Runs tests (if available)
3. Publishes the function app to Azure
4. Sets up environment variables in the Azure Function App configuration

## Testing and Development

### Local Testing

1. **Start required services**:
   ```bash
   # Start Azurite for storage emulation
   docker run -p 10000:10000 -p 10001:10001 -p 10002:10002 \
     mcr.microsoft.com/azure-storage/azurite

   # Start the main FastAPI application (in another terminal)
   cd app
   uvicorn main:app --reload --port 8000
   ```

2. **Start the Azure Functions**:
   ```bash
   cd app
   func start
   ```

3. **Test document processing via HTTP trigger**:
   ```bash
   curl -X POST http://localhost:7071/api/process-document \
     -H "Content-Type: application/json" \
     -d '{
       "source": {
         "text_prompt": "http://127.0.0.1:10000/devstoreaccount1/documents/chat-attachments/uploads-prompts/test-message-id/prompt.txt",
         "documents": [
           "http://127.0.0.1:10000/devstoreaccount1/documents/chat-attachments/uploads/test-message-id/sample.pdf"
         ]
       },
       "signal_r_connection_id": "f2b74be3-b09c-445f-bacb-933fb07a5269"
     }'
   ```

   **Note**: Ensure your `local.env` and `local.settings.json` files are properly configured with the current settings shown in the sample configurations above.

4. **Check orchestration status**:
   ```bash
   curl http://localhost:7071/api/check-status/{instance_id}
   ```

### Queue-based Testing

Alternatively, you can trigger processing by adding messages directly to the queue:

```bash
# Add a message to the content processing queue
# This will trigger the queue_triggers.py function
```

### Integration Testing

The functions integrate with the main application's test suite. Run tests from the app directory:

```bash
cd app
ENVIRONMENT=test pytest -s -v
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `PYTHONPATH=.:app` is set in `local.settings.json`
2. **Configuration Issues**: Verify both `local.env` and `local.settings.json` are properly configured
   - Check that durable functions-specific settings are in `local.env` with nested structure (`SETTING__SUBSETTING`)
   - Check that Azure Functions runtime settings are in `local.settings.json` with flat structure
3. **Database Connection**: Verify database is running and connection string is correct in `local.settings.json`
4. **Storage Connection**: Ensure Azurite is running for local development and connection strings match in both files
5. **Missing Dependencies**: Run `pip install -r requirements.txt` in the app directory

### Logging

The functions use Python's logging module. Check the Azure Functions runtime logs for detailed error information:

```bash
# Local development logs appear in the terminal where you ran `func start`
# Azure deployment logs are available in the Azure portal under Function App > Logs
```
