import logging
from typing import List, cast
from uuid import UUID

from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased

from constants.message import MessageRole, MessageType
from exceptions import ConversationDataInconsistencyError, EntityNotFoundError
from models import QualConversation, QualConversationMessage
from schemas import (
    BaseMessageSerializer,
    CombinedMessageSerializer,
    MessageValidator,
    SystemMessageSerializer,
    UserMessageSerializer,
)

from .conversation import ConversationRepository


logger = logging.getLogger(__name__)


__all__ = ['ConversationMessageRepository']


class ConversationMessageRepository:
    """Repository for conversation message-related database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    async def create(self, message_data: MessageValidator) -> BaseMessageSerializer:
        """
        Create a new conversation message in the database.

        Args:
            message_data: Data for creating the message

        Returns:
            The created message with the conversation's public ID attached
        """
        conversation_id = message_data.conversation_id

        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        await self._validate_conversation_consistency(message_data)

        message = QualConversationMessage(
            QualConversationId=conversation_internal_id,
            **message_data.model_dump_for_db(),
        )
        self.db_session.add(message)
        await self.db_session.flush()
        message.ConversationPublicId = conversation_id  # Attach the conversation's public ID to the message object
        return self._get_serialized_message(message)

    async def get(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get a message by its public ID.

        Args:
            public_id: The public ID of the message

        Returns:
            The message if found, None otherwise
        """
        message_alias = aliased(QualConversationMessage, name='msg')
        query = (
            select(message_alias, QualConversation.PublicId)
            .join(QualConversation, message_alias.QualConversationId == QualConversation.Id)
            .where(message_alias.PublicId == public_id)
        )
        result = await self.db_session.execute(query)
        row = result.first()

        if not row:
            raise EntityNotFoundError('Message', str(public_id))

        message = row.msg
        if not message:
            raise EntityNotFoundError('Message', str(public_id))

        message.ConversationPublicId = row.PublicId
        return self._get_serialized_message(message)

    async def list(self, conversation_id: UUID) -> List[BaseMessageSerializer]:
        """
        Get all messages for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            Sequence of messages for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        query = (
            select(QualConversationMessage)
            .join(QualConversation, QualConversationMessage.QualConversationId == QualConversation.Id)
            .where(QualConversation.PublicId == conversation_id)
            .order_by(QualConversationMessage.CreatedAt)
        )
        result = await self.db_session.execute(query)
        messages = result.scalars().all()

        if not messages:  # Recheck if the conversation exists
            if not await self.conversation_repository.exists(conversation_id):
                raise EntityNotFoundError('Conversation', str(conversation_id))

        # Convert DB models to schema objects
        response_messages = []
        for message in messages:
            message.ConversationPublicId = conversation_id
            response_messages.append(self._get_serialized_message(message))
        return response_messages

    async def get_last(self, conversation_id: UUID) -> BaseMessageSerializer:
        """
        Get the last message for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            The last message for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        query = (
            select(QualConversationMessage)
            .join(QualConversation, QualConversationMessage.QualConversationId == QualConversation.Id)
            .where(QualConversation.PublicId == conversation_id)
            .order_by(QualConversationMessage.CreatedAt.desc(), QualConversationMessage.Id.desc())
            .limit(1)
        )
        result = await self.db_session.execute(query)
        message = result.scalar_one_or_none()

        if not message:
            if not await self.conversation_repository.exists(conversation_id):
                raise EntityNotFoundError('Conversation', str(conversation_id))
            raise EntityNotFoundError('The last message of conversation', str(conversation_id))

        message.ConversationPublicId = conversation_id
        return self._get_serialized_message(message)

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all messages associated with a conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            None
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        # Delete all messages with the conversation's internal ID
        delete_query = delete(QualConversationMessage).where(
            QualConversationMessage.QualConversationId == conversation_internal_id
        )
        await self.db_session.execute(delete_query)

    async def update_fields(self, public_id: UUID, updates: dict) -> None:
        """
        Update multiple fields of a message by its public ID.

        Args:
            public_id: The public ID of the message to update
            **updates: Field-value pairs to update

        Returns:
            None

        Raises:
            EntityNotFoundError: If the message doesn't exist
        """
        # First check if the message exists
        message_exists_query = select(QualConversationMessage.Id).where(QualConversationMessage.PublicId == public_id)
        result = await self.db_session.execute(message_exists_query)
        message_id = result.scalar_one_or_none()

        if message_id is None:
            raise EntityNotFoundError('Message', str(public_id))

        # Update the message fields
        update_query = (
            update(QualConversationMessage).where(QualConversationMessage.PublicId == public_id).values(**updates)
        )
        await self.db_session.execute(update_query)

    @staticmethod
    def _get_serialized_message(message: QualConversationMessage) -> BaseMessageSerializer:
        return (
            SystemMessageSerializer if message.Role == MessageRole.SYSTEM else UserMessageSerializer  # pyright: ignore[reportGeneralTypeIssues]
        ).model_validate(message)

    async def get_owner_id(self, message_id: UUID) -> UUID | None:
        """
        Get an owner ID for the message.

        Args:
            message_id: The public ID of the message

        Returns:
            The user ID if found, None otherwise
        """
        query = (
            select(QualConversation.CreatedById)
            .join(QualConversationMessage, QualConversation.Id == QualConversationMessage.QualConversationId)
            .where(QualConversationMessage.PublicId == message_id)
            .order_by(QualConversationMessage.CreatedAt)
        )
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()

    async def get_combined_history(self, conversation_id: UUID) -> List[CombinedMessageSerializer]:
        """
        Get conversation history as CombinedMessageSerializer objects for suggested prompts tracking.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            List of CombinedMessageSerializer objects representing the conversation history

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the messages
        """
        if not await self.conversation_repository.exists(conversation_id):
            raise EntityNotFoundError('Conversation', str(conversation_id))

        messages = await self.list(conversation_id)

        welcome_message = messages.pop(0)  # get rid of welcome message
        if welcome_message.role != MessageRole.SYSTEM:
            raise ValueError('Conversation is corrupted. Welcome message role is not SYSTEM')

        if welcome_message.type != MessageType.TEXT:
            raise ValueError('Conversation is corrupted. Welcome message role is not SYSTEM')

        if len(messages) > 0:
            last_message = messages.pop(-1)  # get rid of last user message that does not yet have a reply.
            if last_message.role != MessageRole.USER:
                raise ValueError(f'Invalid last message role - expected {MessageRole.USER}, got {last_message.role}')

        conversation_history = [
            CombinedMessageSerializer(
                user=cast(UserMessageSerializer, messages[i]),
                system=cast(SystemMessageSerializer, messages[i + 1]),
            )
            for i in range(0, len(messages), 2)
        ]

        logger.info(
            'Retrieved %s messages in history of conversation with ID: %s', len(conversation_history), conversation_id
        )
        return conversation_history

    async def _validate_conversation_consistency(self, message_data: MessageValidator):
        """
        Validate conversation message order.

        Args:
            message_data: Data for the new message
            conversation_id: Public ID of the conversation

        Returns:
            None
        """
        try:
            last_message = await self.get_last(message_data.conversation_id)
        except EntityNotFoundError:
            return

        if last_message and last_message.role == message_data.role:
            raise ConversationDataInconsistencyError(
                'Conversation is corrupted: the alternation order of message roles is violated'
            )
