from typing import Sequence
from uuid import UUID

from sqlalchemy import delete, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased

from models import QualConversation, QualConversationMessage, QualDocument, QualProcessingMessage


__all__ = ['DocumentDbRepository']


class DocumentDbRepository:
    """Repository for document-related database operations."""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create(
        self,
        message_public_id: UUID,
        file_name: str,
        file_size: int,
        file_type: str,
        file_url: str,
    ) -> QualDocument:
        """
        Create a new document in the database.

        Args:
            message_public_id: The public ID of the associated conversation message
            file_name: Name of the file
            file_size: Size of the file in bytes
            file_type: MIME type of the file
            file_url: URL to access the file in blob storage

        Returns:
            The created document
        """
        # Get the message's internal ID using its public ID
        message_alias = aliased(QualConversationMessage, name='msg')
        query = select(message_alias.Id).where(message_alias.PublicId == message_public_id)
        result = await self.db_session.execute(query)
        message_id = result.scalar_one()

        new_document = QualDocument(
            QualConversationMessageId=message_id,
            FileName=file_name,
            FileSize=file_size,
            FileType=file_type,
            FileUrl=file_url,
        )

        self.db_session.add(new_document)
        await self.db_session.flush()

        # Attach the message's public ID to the document object
        new_document.MessagePublicId = message_public_id

        return new_document

    async def count_documents_by_conversation_id(self, conversation_public_id: UUID) -> int:
        """
        Count the number of documents in a conversation.

        Args:
            conversation_public_id: The public ID of the conversation

        Returns:
            The number of documents in the conversation
        """
        document_alias = aliased(QualDocument, name='doc')
        message_alias = aliased(QualConversationMessage, name='msg')
        conversation_alias = aliased(QualConversation, name='conv')

        query = (
            select(func.count())
            .select_from(document_alias)
            .join(message_alias, document_alias.QualConversationMessageId == message_alias.Id)
            .join(conversation_alias, message_alias.QualConversationId == conversation_alias.Id)
            .where(conversation_alias.PublicId == conversation_public_id)
        )

        result = await self.db_session.execute(query)
        return result.scalar_one() or 0

    async def get_total_size_by_conversation_id(self, conversation_public_id: UUID) -> int:
        """
        Calculate the total size of all documents in a conversation.

        Args:
            conversation_public_id: The public ID of the conversation

        Returns:
            The total size of all documents in the conversation in bytes
        """
        document_alias = aliased(QualDocument, name='doc')
        message_alias = aliased(QualConversationMessage, name='msg')
        conversation_alias = aliased(QualConversation, name='conv')

        query = (
            select(func.sum(document_alias.FileSize))
            .select_from(document_alias)
            .join(message_alias, document_alias.QualConversationMessageId == message_alias.Id)
            .join(conversation_alias, message_alias.QualConversationId == conversation_alias.Id)
            .where(conversation_alias.PublicId == conversation_public_id)
        )

        result = await self.db_session.execute(query)
        return result.scalar_one() or 0

    async def get_file_info_for_deletion(self, conversation_id: UUID) -> Sequence[tuple[str, UUID]]:
        """
        Get all file names and their associated message IDs for a conversation.

        Args:
            conversation_id: The UUID of the conversation

        Returns:
            A list of tuples (file_name, message_public_id) for blob path construction
        """
        document_alias = aliased(QualDocument, name='doc')
        message_alias = aliased(QualConversationMessage, name='msg')
        conversation_alias = aliased(QualConversation, name='conv')

        query = (
            select(document_alias.FileName, message_alias.PublicId)
            .select_from(document_alias)
            .join(message_alias, document_alias.QualConversationMessageId == message_alias.Id)
            .join(conversation_alias, message_alias.QualConversationId == conversation_alias.Id)
            .where(conversation_alias.PublicId == conversation_id)
        )

        result = await self.db_session.execute(query)
        return tuple((row[0], row[1]) for row in result.all())

    async def get_filenames_for_message(self, message_public_id: UUID) -> Sequence[str]:
        """
        Get all file names associated with a specific message.

        Args:
            message_public_id: The public UUID of the message

        Returns:
            A list of filenames associated with the message
        """
        document_alias = aliased(QualDocument, name='doc')
        message_alias = aliased(QualConversationMessage, name='msg')

        query = (
            select(document_alias.FileName)
            .select_from(document_alias)
            .join(message_alias, document_alias.QualConversationMessageId == message_alias.Id)
            .where(message_alias.PublicId == message_public_id)
        )

        result = await self.db_session.execute(query)
        return tuple(row[0] for row in result.all())

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all documents associated with messages in a conversation.
        """
        message_alias = aliased(QualConversationMessage, name='msg')
        conversation_alias = aliased(QualConversation, name='conv')

        # First get the message IDs for this conversation
        message_id_query = (
            select(message_alias.Id)
            .join(conversation_alias, message_alias.QualConversationId == conversation_alias.Id)
            .where(conversation_alias.PublicId == conversation_id)
        )

        result = await self.db_session.execute(message_id_query)
        message_ids = result.scalars().all()

        if not message_ids:
            # No messages found for this conversation, nothing to delete
            return

        # Delete all documents for these messages
        delete_query = delete(QualDocument).where(QualDocument.QualConversationMessageId.in_(message_ids))

        await self.db_session.execute(delete_query)
        await self.db_session.flush()

    async def remove_corrupted_documents_for_message(self, message_public_id: UUID) -> int:
        """
        Remove QualDocument records for files that failed processing due to corruption.

        This method identifies documents associated with a message that have
        ProcessingStatus.DocumentIsCorrupted and removes them from the database
        so they don't count toward the file upload limit.

        Args:
            message_public_id: The public ID of the message

        Returns:
            The number of corrupted document records removed
        """
        # Get the message's internal ID
        message_alias = aliased(QualConversationMessage, name='msg')
        message_id_query = select(message_alias.Id).where(message_alias.PublicId == message_public_id)
        result = await self.db_session.execute(message_id_query)
        message_id = result.scalar_one_or_none()

        if not message_id:
            return 0

        # Find documents for messages that have DocumentIsCorrupted status
        processing_alias = aliased(QualProcessingMessage, name='proc')
        document_alias = aliased(QualDocument, name='doc')

        # Get document IDs that have corrupted processing status
        corrupted_docs_query = (
            select(document_alias.Id)
            .select_from(document_alias)
            .join(
                processing_alias, processing_alias.QualConversationMessageId == document_alias.QualConversationMessageId
            )
            .where(
                document_alias.QualConversationMessageId == message_id, processing_alias.Status == 'DocumentIsCorrupted'
            )
        )

        result = await self.db_session.execute(corrupted_docs_query)
        corrupted_doc_ids = result.scalars().all()

        if not corrupted_doc_ids:
            return 0

        # Delete the corrupted document records
        delete_query = delete(QualDocument).where(QualDocument.Id.in_(corrupted_doc_ids))
        delete_result = await self.db_session.execute(delete_query)
        await self.db_session.flush()

        return delete_result.rowcount or 0
