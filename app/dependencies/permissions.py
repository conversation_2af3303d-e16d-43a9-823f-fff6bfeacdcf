from uuid import UUI<PERSON>

from fastapi import Depends, Request

from constants.operation_ids import operation_ids
from exceptions import EntityNotFoundError

from .services import ConversationMessageServiceDep, ConversationServiceDep


__all__ = ['OwnerOnlyPermissionDep']


class OwnerOnlyPermission:
    _MESSAGE_IN_PATH_OPERATION_IDS = {
        operation_ids.message.GET,
    }
    _CONVERSATION_IN_PATH_OPERATION_IDS = {
        operation_ids.conversation.GET,
        operation_ids.conversation.DELETE,
        operation_ids.message.LIST,
        operation_ids.message.GET_LAST,
    }
    _CONVERSATION_IN_QUERY_OPERATION_IDS = {
        operation_ids.auth.CREATE_SIGNAL_R_JWT,
    }

    async def __call__(
        self,
        request: Request,
        conversation_service: ConversationServiceDep,
        message_service: ConversationMessageServiceDep,
    ):
        method, entity_id, entity_type_name = self._get_permission_check_method_data(
            request=request,
            conversation_service=conversation_service,
            message_service=message_service,
        )
        owner_id = await method(entity_id)
        if owner_id != request.state.user.id:
            raise EntityNotFoundError(entity_type_name, entity_id)

    @classmethod
    def _get_permission_check_method_data(
        cls,
        request: Request,
        conversation_service: ConversationServiceDep,
        message_service: ConversationMessageServiceDep,
    ):
        route = request.scope.get('route')
        operation_id = route.operation_id if route else None
        if operation_id in cls._MESSAGE_IN_PATH_OPERATION_IDS:
            method = message_service.get_owner_id
            entity_type_name = 'Message'
            param_name = 'message_id'
            entity_id = request.path_params[param_name]
        elif operation_id in cls._CONVERSATION_IN_PATH_OPERATION_IDS:
            method = conversation_service.get_owner_id
            entity_type_name = 'Conversation'
            param_name = 'conversation_id'
            entity_id = request.path_params[param_name]
        elif operation_id in cls._CONVERSATION_IN_QUERY_OPERATION_IDS:
            method = conversation_service.get_owner_id
            entity_type_name = 'Conversation'
            param_name = 'conversation_id'
            entity_id = request.query_params[param_name]
        else:
            raise NotImplementedError(f'"Owner only" permission check not yet supported for operation "{operation_id}"')

        try:
            entity_id = UUID(entity_id)
        except ValueError:
            raise EntityNotFoundError(entity_type_name, entity_id)

        return method, entity_id, entity_type_name


OwnerOnlyPermissionDep = Depends(OwnerOnlyPermission())
